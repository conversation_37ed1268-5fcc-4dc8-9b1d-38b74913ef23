<?php

namespace App\Filament\Staff\Resources\OverviewResource\Pages;

use App\Filament\Staff\Resources\OverviewResource;
use App\Models\Course;
use App\Models\Grade;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\URL;

class ManageOverview extends ManageRecords
{
    protected static string $resource = OverviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('view')
                    ->disabled(fn () => $this->hasNoRequiredFiltersNorFilteredRecords())
                    ->icon('heroicon-o-eye')
                    ->modalHeading('Overview')
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close')
                    ->modalWidth(MaxWidth::SixExtraLarge)
                    ->modalContent(function () {
                        $filters = $this->extractFilters();

                        $filteredRecords = $this->getFilteredTableQuery()->get();
                        $courses = Course::query()
                            ->where('department_id', $filters['department_id'])
                            ->where('level_id', $filters['level_id'])
                            ->where('semester_id', $filters['semester_id'])
                            ->get();

                        $grades = Grade::orderBy('min_score')->get();
                        $failedScore = Grade::where('min_score', 0)->value('max_score') ?? 39;

                        $semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses);
                        $sampleRecord = $filteredRecords->first();
                        $cumulativeTotalCreditUnit = $sampleRecord
                            ? OverviewResource::getCumulativeTotalCreditUnit($this, $sampleRecord)
                            : 0;

                        return view('filament.pages.overview', [
                            'livewire' => $this,
                            'filters' => $filters,
                            'filteredRecords' => $filteredRecords,
                            'courses' => $courses,
                            'grades' => $grades,
                            'failedScore' => $failedScore,
                            'semesterTotalCreditUnit' => $semesterTotalCreditUnit,
                            'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit,
                        ]);
                    }),
                Action::make('print')
                    ->disabled(fn () => $this->hasNoRequiredFiltersNorFilteredRecords())
                    ->icon('heroicon-m-printer')
                    ->action(function () {
                        try {
                            $overviewCacheKey = 'overviewData_'.uniqid();
                            $this->cacheOverviewData($overviewCacheKey);

                            $url = URL::signedRoute('overview.print', ['overviewCacheKey' => $overviewCacheKey]);

                            return $this->js("(function() {
                                const newWindow = window.open(
                                    '$url',
                                    'Overview',
                                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                );

                                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                    alert('Pop-up blocked! Please allow pop-ups to print the overview.');
                                } else {
                                    newWindow.focus();
                                }
                            })();");
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Print Error')
                                ->body('Unable to print overview. Please try again later.')
                                ->danger()
                                ->send();
                        }
                    }),
                Action::make('download')
                    ->disabled(fn () => $this->hasNoRequiredFiltersNorFilteredRecords())
                    ->icon('heroicon-m-document-arrow-down')
                    ->action(function () {
                        try {
                            $overviewCacheKey = 'overviewData_'.uniqid();
                            $this->cacheOverviewData($overviewCacheKey);

                            $url = URL::signedRoute('overview.download', ['overviewCacheKey' => $overviewCacheKey]);

                            return $this->js("window.location.href = '$url';");
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Download Error')
                                ->body('Unable to download overview. Please try again later.')
                                ->danger()
                                ->send();
                        }
                    }),
            ])
                ->button()
                ->label(fn () => $this->hasNoRequiredFiltersNorFilteredRecords() ? 'Cannot export: view overview first' : 'Export overview')
                ->icon('heroicon-m-document-text'),
        ];
    }

    private function hasNoRequiredFiltersNorFilteredRecords(): bool
    {

        if (! $this->hasRequiredFilters()) {
            return true;
        }

        return $this->getFilteredRecords()->isEmpty();
    }

    private function getFilteredRecords()
    {
        return $this->getFilteredTableQuery()->get();
    }

    private function hasRequiredFilters(): bool
    {
        return ! in_array(null, $this->extractFilters(), true);
    }

    private function extractFilters(): array
    {
        $filters = $this->tableFilters['overview_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
        ];
    }

    private function cacheOverviewData(string $cacheKey): void
    {
        $filters = $this->extractFilters();

        $filteredRecords = $this->getFilteredTableQuery()->get();
        $courses = Course::query()
            ->where('department_id', $filters['department_id'])
            ->where('level_id', $filters['level_id'])
            ->where('semester_id', $filters['semester_id'])
            ->get();

        $grades = Grade::orderBy('min_score')->get();
        $failedScore = Grade::where('min_score', 0)->value('max_score') ?? 39;

        $semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses);
        $sampleRecord = $filteredRecords->first();
        $cumulativeTotalCreditUnit = $sampleRecord
            ? OverviewResource::getCumulativeTotalCreditUnit($this, $sampleRecord)
            : 0;

        $overviewData = [
            'livewire' => $this,
            'filters' => $filters,
            'filteredRecords' => $filteredRecords,
            'courses' => $courses,
            'grades' => $grades,
            'failedScore' => $failedScore,
            'semesterTotalCreditUnit' => $semesterTotalCreditUnit,
            'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit,
        ];

        Cache::put($cacheKey, $overviewData, now()->addMinutes(5));
    }
}
