<?php if (! $__env->hasRenderedOnce('3d38b1f9-eb68-4bfc-97f8-849b1ffafeeb')): $__env->markAsRenderedOnce('3d38b1f9-eb68-4bfc-97f8-849b1ffafeeb');
$__env->startPush('styles'); ?>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono&display=swap');

        .shiki * {
            font-family: "JetBrains Mono", monospace;
            font-optical-sizing: auto;
            font-weight: 400;
            font-style: normal;
        }
    </style>
<?php $__env->stopPush(); endif; ?>

<div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
    "bg-gray-900 text-gray-200 dark:bg-gray-900 rounded-lg p-2 pt-0 mb-4 ring-1 ring-gray-950/5 dark:ring-white/10",
]); ?>"
     x-data
>
    <div class="text-xs text-white/50 py-2 px-1 flex flex-row items-center justify-between">
        <span><?php echo e($language); ?></span>
        <span
            class="hover:cursor-pointer hover:text-white/70 flex items-center justify-center gap-1 text-xs"
            x-on:click.prevent="
        if (navigator.clipboard) {
        navigator.clipboard.writeText($root.querySelector('.shiki').textContent);
        new FilamentNotification().title(filamentKnowledgeBaseTranslations.codeCopied).success().send();
        }
        const range = document.createRange();
        range.selectNodeContents($root.querySelector('.shiki'));
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
        ">
            <?php if (isset($component)) { $__componentOriginal606b6d7eddc2e418f11096356be15e19 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal606b6d7eddc2e418f11096356be15e19 = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Icon::resolve(['name' => 'heroicon-o-clipboard'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal606b6d7eddc2e418f11096356be15e19)): ?>
<?php $attributes = $__attributesOriginal606b6d7eddc2e418f11096356be15e19; ?>
<?php unset($__attributesOriginal606b6d7eddc2e418f11096356be15e19); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal606b6d7eddc2e418f11096356be15e19)): ?>
<?php $component = $__componentOriginal606b6d7eddc2e418f11096356be15e19; ?>
<?php unset($__componentOriginal606b6d7eddc2e418f11096356be15e19); ?>
<?php endif; ?>
            <span x-show="navigator.clipboard">Copy</span>
            <span x-show="! navigator.clipboard">Select</span>
        </span>
    </div>
    <div class="rounded-lg p-4 leading-relaxed bg-gray-700/80 [&_.shiki]:!bg-transparent">
        <?php echo $code; ?>

    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\racoed\vendor\guava\filament-knowledge-base\resources\views\code-block.blade.php ENDPATH**/ ?>