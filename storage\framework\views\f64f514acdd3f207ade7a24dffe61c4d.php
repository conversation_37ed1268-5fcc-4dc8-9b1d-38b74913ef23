<?php

use App\Filament\Student\Clusters\Cpay\Resources\PortalResource;
use Illuminate\Support\Facades\Auth;

$user = Auth::user();

?>

<!--[if BLOCK]><![endif]--><?php if($user->hasUnpaidPortalFee()): ?>
<div class="bg-amber-100 border border-amber-400 text-amber-800 px-4 py-2 rounded-sm flex items-center justify-center gap-2 mt-4 text-sm font-semibold">
    <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => 'heroicon-s-exclamation-circle','class' => 'w-5 h-5 text-amber-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-s-exclamation-circle','class' => 'w-5 h-5 text-amber-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
    You have unpaid portal fees. Please pay now to get full access.
    <a href="<?php echo e(PortalResource::getUrl('index')); ?>" class="text-blue-600 underline">View portal fees</a>
</div>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->



<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/hooks/global-portal-access-banner.blade.php ENDPATH**/ ?>