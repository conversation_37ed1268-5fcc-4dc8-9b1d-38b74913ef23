<?php use \Guava\FilamentKnowledgeBase\Facades\KnowledgeBase; ?>
<article
    <?php echo e($attributes->class([
        'gu-kb-article',
        '[&_ul]:list-[revert] [&_ol]:list-[revert] [&_ul]:ml-4 [&_ol]:ml-4' => ! KnowledgeBase::panel()->shouldDisableDefaultClasses(),
    ])); ?>

    x-ignore
    ax-load
    ax-load-src="<?php echo e(\Filament\Support\Facades\FilamentAsset::getAlpineComponentSrc('anchors-component', 'guava/filament-knowledge-base')); ?>"
    x-data="anchorsComponent()"
>
    <?php echo e($slot); ?>

</article>
<?php /**PATH C:\Users\<USER>\Herd\racoed\vendor\guava\filament-knowledge-base\resources\views\components\content.blade.php ENDPATH**/ ?>