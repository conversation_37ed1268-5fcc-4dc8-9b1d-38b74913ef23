<?php

namespace App\Filament\Student\Clusters\Cpay\Resources;

use App\Enums\AdmissionStatus;
use App\Enums\InvoiceStatus;
use App\Enums\Role;
use App\Filament\Student\Clusters\Cpay;
use App\Filament\Student\Clusters\Cpay\Resources\InvoiceResource\Pages;
use App\Models\Fee;
use App\Models\Invoice;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Semester;
use Filament\Notifications\Notification;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?int $navigationSort = 3;

    protected static ?string $cluster = Cpay::class;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('invoice_status', InvoiceStatus::PAID)
            ->whereHasMorph('payable', [Fee::class, Registration::class], function ($query) {
                $query->where('user_id', Auth::id());
            })->with(['fee', 'payable', 'payable.level', 'payable.semester', 'payable.schoolSession']);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function table(Table $table): Table
    {
        $student = Auth::user();

        return $table
            ->deferLoading()
            ->deferFilters()
            ->persistFiltersInSession()
            ->paginated([5, 10, 25])
            ->striped()
            ->recordAction(null)
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('No Invoices Yet')
            ->emptyStateDescription('Once you make your first payment, it will appear here.')
            ->description('List of all invoices generated for your payments and fees in the college.')
            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('number')
                    ->label('Invoice no.'),
                TextColumn::make('total_amount')
                    ->prefix('₦')
                    ->formatStateUsing(fn ($state) => number_format($state)),
                TextColumn::make('description')
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(
                        fn ($record) => is_array($record->fee?->description ?? $record->description)
                            ? array_map(
                                fn ($item) => $item['item'].' - ₦'.number_format($item['amount']),
                                $record->fee?->description ?? $record->description
                            )
                            : [$record->fee?->description ?? $record->description]
                    ),
                TextColumn::make('period')
                    ->listWithLineBreaks()
                    ->bulleted(),
                TextColumn::make('fee_type')
                    ->label('Fee type')
                    ->badge(),
                TextColumn::make('invoice_status')
                    ->badge()
                    ->label('Status'),
                TextColumn::make('paid_at')
                    ->label('Date')
                    ->date()
                    ->sinceTooltip()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('school_session_id')
                    ->label('Session')
                    ->options(function () use ($student) {
                        return SchoolSession::whereIn('id', function ($query) use ($student) {
                            $query->select('school_session_id')
                                ->from('registrations')
                                ->where('user_id', $student->id)
                                ->distinct();
                        })
                            ->orderBy('name', 'desc')
                            ->pluck('name', 'id');
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $value): Builder => $query->whereHasMorph(
                                'payable',
                                [Registration::class],
                                fn (Builder $query) => $query->where('school_session_id', $value)
                            )
                        );
                    })
                    ->native(false)
                    ->default($student->activeSchoolSession->id),
                SelectFilter::make('semester_id')
                    ->label('Semester')
                    ->options(function () use ($student) {
                        return Semester::whereIn('id', function ($query) use ($student) {
                            $query->select('semester_id')
                                ->from('registrations')
                                ->where('user_id', $student->id)
                                ->distinct();
                        })
                            ->orderBy('name')
                            ->pluck('name', 'id');
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $value): Builder => $query->whereHasMorph(
                                'payable',
                                [Registration::class],
                                fn (Builder $query) => $query->where('semester_id', $value)
                            )
                        );
                    })
                    ->native(false)
                    ->default($student->activeSemester->id),
            ], layout: FiltersLayout::AboveContent)

            ->actions([
                ActionGroup::make([
                    Tables\Actions\Action::make('print')
                        ->disabled(fn ($record) => $record->invoice_status !== InvoiceStatus::PAID)
                        ->icon('heroicon-m-printer')
                        ->action(function ($record, $livewire) {
                            try {
                                // Generate signed URL
                                $url = URL::signedRoute('invoice.print', ['invoice' => $record->id]);

                                $livewire->js("(function() {
                                    const newWindow = window.open(
                                        '$url',
                                        'Invoice',
                                        'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                    );

                                    if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                        alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                    } else {
                                        newWindow.focus();
                                    }
                                })();");
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Print Error')
                                    ->body('Unable to print invoice. Please try again later.')
                                    ->danger()
                                    ->send();
                            }
                        }),
                    Tables\Actions\Action::make('download')
                        ->disabled(fn ($record) => $record->invoice_status !== InvoiceStatus::PAID)
                        ->icon('heroicon-m-document-arrow-down')
                        ->action(function ($record, $livewire) {
                            try {
                                $url = URL::signedRoute('invoice.download', ['invoice' => $record->id]);
                                $livewire->js("window.location.href = '$url';");
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Download Error')
                                    ->body('Unable to download invoice. Please try again later.')
                                    ->danger()
                                    ->send();
                            }
                        }),
                ])
                    ->icon('heroicon-m-document-text')
                    ->tooltip('Export receipt'),

            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageInvoices::route('/'),
        ];
    }
}
