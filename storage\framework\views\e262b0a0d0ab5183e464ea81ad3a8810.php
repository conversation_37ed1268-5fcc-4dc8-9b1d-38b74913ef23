<?php
$sidebar = $this->getSubNavigationPosition();
?>
<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => ['class' => \Illuminate\Support\Arr::toCssClasses([
        '[&_.fi-page-sub-navigation-sidebar]:pl-4 [&_.fi-page-sub-navigation-sidebar]:border-l [&_.fi-page-sub-navigation-sidebar]:border-l-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-l-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::End,
        '[&_.fi-page-sub-navigation-sidebar]:pr-4 [&_.fi-page-sub-navigation-sidebar]:border-r [&_.fi-page-sub-navigation-sidebar]:border-r-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-r-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::Start,
    ]),'fullHeight' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
        '[&_.fi-page-sub-navigation-sidebar]:pl-4 [&_.fi-page-sub-navigation-sidebar]:border-l [&_.fi-page-sub-navigation-sidebar]:border-l-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-l-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::End,
        '[&_.fi-page-sub-navigation-sidebar]:pr-4 [&_.fi-page-sub-navigation-sidebar]:border-r [&_.fi-page-sub-navigation-sidebar]:border-r-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-r-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::Start,
    ])),'full-height' => true]); ?>
    <div>
    <?php echo $this->html; ?>

    </div>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\racoed\vendor\guava\filament-knowledge-base\resources\views\pages\section.blade.php ENDPATH**/ ?>