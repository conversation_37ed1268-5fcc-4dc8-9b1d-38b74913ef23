<?php if (isset($component)) { $__componentOriginal5837319ee62a160ade163b4570aeaa61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5837319ee62a160ade163b4570aeaa61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.overview','data' => ['livewire' => $livewire,'filters' => $filters,'filteredRecords' => $filteredRecords,'courses' => $courses,'grades' => $grades,'failedScore' => $failedScore,'semesterTotalCreditUnit' => $semesterTotalCreditUnit,'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('overview'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['livewire' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($livewire),'filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($filters),'filteredRecords' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($filteredRecords),'courses' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($courses),'grades' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($grades),'failedScore' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($failedScore),'semesterTotalCreditUnit' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($semesterTotalCreditUnit),'cumulativeTotalCreditUnit' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($cumulativeTotalCreditUnit)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5837319ee62a160ade163b4570aeaa61)): ?>
<?php $attributes = $__attributesOriginal5837319ee62a160ade163b4570aeaa61; ?>
<?php unset($__attributesOriginal5837319ee62a160ade163b4570aeaa61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5837319ee62a160ade163b4570aeaa61)): ?>
<?php $component = $__componentOriginal5837319ee62a160ade163b4570aeaa61; ?>
<?php unset($__componentOriginal5837319ee62a160ade163b4570aeaa61); ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/pages/overview.blade.php ENDPATH**/ ?>