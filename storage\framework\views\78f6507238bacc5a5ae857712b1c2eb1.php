<?php
    use Filament\Facades\Filament;

    $hasModalPreviews = Filament::getPlugin('guava::filament-knowledge-base')->hasModalPreviews();
    $hasSlideOverPreviews = Filament::getPlugin('guava::filament-knowledge-base')->hasSlideOverPreviews();
    $hasModalTitleBreadcrumbs = Filament::getPlugin('guava::filament-knowledge-base')->hasModalTitleBreadcrumbs();
    $target = Filament::getPlugin('guava::filament-knowledge-base')->shouldOpenDocumentationInNewTab() ? '_blank' : '_self';
    $articleClass = \Guava\FilamentKnowledgeBase\Facades\KnowledgeBase::panel()->getArticleClass();
?>

<div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
    'hidden' => ! $documentation,
]); ?>"
>
    <!--[if BLOCK]><![endif]--><?php if($documentation): ?>
        <!--[if BLOCK]><![endif]--><?php if($this->shouldShowAsMenu()): ?>
            <?php echo e($this->getMenuAction()); ?>

        <?php else: ?>
            <?php echo e($this->getSingleAction()); ?>

        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if (isset($component)) { $__componentOriginal028e05680f6c5b1e293abd7fbe5f9758 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-actions::components.modals','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-actions::modals'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758)): ?>
<?php $attributes = $__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758; ?>
<?php unset($__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal028e05680f6c5b1e293abd7fbe5f9758)): ?>
<?php $component = $__componentOriginal028e05680f6c5b1e293abd7fbe5f9758; ?>
<?php unset($__componentOriginal028e05680f6c5b1e293abd7fbe5f9758); ?>
<?php endif; ?>

    <?php $__env->startPush('scripts'); ?>
        <div class="gu-kb-modals h-0 overflow-hidden">
            <!--[if BLOCK]><![endif]--><?php if($hasModalPreviews): ?>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getDocumentation(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $documentable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginal0942a211c37469064369f887ae8d1cef = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0942a211c37469064369f887ae8d1cef = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.modal.index','data' => ['id' => ''.e($documentable->getId()).'','closeByClickingAway' => true,'closeButton' => true,'width' => '2xl','slideOver' => $hasSlideOverPreviews,'class' => '[&_.fi-modal-content]:px-12 [&_.fi-modal-content]:gap-y-0','footerActionsAlignment' => \Filament\Support\Enums\Alignment::End,'stickyFooter' => true,'stickyHeader' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => ''.e($documentable->getId()).'','close-by-clicking-away' => true,'close-button' => true,'width' => '2xl','slide-over' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasSlideOverPreviews),'class' => '[&_.fi-modal-content]:px-12 [&_.fi-modal-content]:gap-y-0','footer-actions-alignment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Filament\Support\Enums\Alignment::End),'sticky-footer' => true,'sticky-header' => true]); ?>

                         <?php $__env->slot('heading', null, []); ?> 
                            <!--[if BLOCK]><![endif]--><?php if($hasModalTitleBreadcrumbs && !empty($documentable->getBreadcrumbs())): ?>
                                <?php echo e(KnowledgeBase::breadcrumbs($documentable)); ?>

                            <?php else: ?>
                                <?php echo e($documentable->getTitle()); ?>

                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                         <?php $__env->endSlot(); ?>

                        <?php if (isset($component)) { $__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-knowledge-base::components.content','data' => ['class' => \Illuminate\Support\Arr::toCssClasses([
        'gu-kb-article-modal',
        $articleClass => ! empty($articleClass),
    ])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-knowledge-base::content'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
        'gu-kb-article-modal',
        $articleClass => ! empty($articleClass),
    ]))]); ?>
                            <?php echo $documentable->getSimpleHtml(); ?>

                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5)): ?>
<?php $attributes = $__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5; ?>
<?php unset($__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5)): ?>
<?php $component = $__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5; ?>
<?php unset($__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5); ?>
<?php endif; ?>
                         <?php $__env->slot('footerActions', null, []); ?> 
                            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['tag' => 'a','href' => $documentable->getUrl(),'target' => $target]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'a','href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($documentable->getUrl()),'target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($target)]); ?>
                                <?php echo e(__('filament-knowledge-base::translations.open-documentation')); ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'gray','xOn:click.prevent' => '$dispatch(\'close-modal\', { id: \''.e($documentable->getId()).'\' })']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'gray','x-on:click.prevent' => '$dispatch(\'close-modal\', { id: \''.e($documentable->getId()).'\' })']); ?>

                                <?php echo e(__('filament-knowledge-base::translations.close')); ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                         <?php $__env->endSlot(); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0942a211c37469064369f887ae8d1cef)): ?>
<?php $attributes = $__attributesOriginal0942a211c37469064369f887ae8d1cef; ?>
<?php unset($__attributesOriginal0942a211c37469064369f887ae8d1cef); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0942a211c37469064369f887ae8d1cef)): ?>
<?php $component = $__componentOriginal0942a211c37469064369f887ae8d1cef; ?>
<?php unset($__componentOriginal0942a211c37469064369f887ae8d1cef); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php $__env->stopPush(); ?>
</div>
<?php /**PATH C:\Users\<USER>\Herd\racoed\vendor\guava\filament-knowledge-base\src\/../resources/views/livewire/help-menu.blade.php ENDPATH**/ ?>