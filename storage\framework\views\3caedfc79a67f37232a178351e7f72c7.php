<?php
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

$now = Carbon::now();
$hour = $now->format('H');
$today = $now->format('l, F j, Y');

if ($hour < 12) {
    $greeting='Good morning' ;
} elseif ($hour < 18) {
    $greeting='Good afternoon' ;
} else {
    $greeting='Good evening' ; }

    ?>

<div class="hidden sm:flex flex-row text-xs text-gray-600 dark:text-gray-200 order-first">
    <div class="flex flex-col">
        <span>
            <?php echo e($greeting); ?>,
            <strong><?php echo e(explode(' ', Auth::user()->name)[0]); ?> </strong>&nbsp;

            <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['color' => 'gray','style' => 'display: inline-block;']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'gray','style' => 'display: inline-block;']); ?>
                <?php echo e(Auth::user()->role->getLabel()); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
        </span>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/hooks/welcome-user.blade.php ENDPATH**/ ?>