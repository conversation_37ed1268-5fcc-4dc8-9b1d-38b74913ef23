<?php
    use App\Services\AcademicCalendarService;
    use App\Settings\CollegeSettings;

    $calendar = app(AcademicCalendarService::class);

    $panelId = filament()->getCurrentPanel()?->getId();
    $activeSession = $calendar->getCurrentSession();
    $activeSemester = $calendar->getActiveSemesterText();
?>

<?php if($panelId !== 'help'): ?>
<div class="hidden lg:flex flex-col text-xs text-gray-600 dark:text-gray-200">
    <a href="<?php echo e(config('app.url')); ?>" class="font-semibold mb-2">
        <?php echo e(app(CollegeSettings::class)->name); ?>

    </a>
    <div>
        <span class="font-semibold">Session:</span>
        <span><?php echo e($activeSession); ?> |</span>
        <span class="font-semibold">Semester:</span>
        <span><?php echo e($activeSemester); ?></span>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/hooks/school-details.blade.php ENDPATH**/ ?>