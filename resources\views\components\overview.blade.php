@php
use App\Filament\Staff\Resources\OverviewResource;
@endphp

<div class="p-6 overflow-x-auto">
    <table class="table-auto text-sm border border-gray-300 w-full">
        <thead class="bg-gray-100">
            <tr>
                <th class="border px-2 py-1">#</th>
                <th class="border px-2 py-1">Name</th>
                <th class="border px-2 py-1 ">Matric No.</th>

                @foreach ($courses as $course)
                <th colspan="4" class="border px-2 py-1 text-center" x-tooltip.raw="{{ $course->title }}">
                    {{ $course->code }}
                    <span class="text-gray-400">
                        {{ $course->credit }}{{ $course->course_status->value }}
                    </span>
                </th>
                @endforeach

                <th colspan="4" class="border px-2 py-1 text-center" x-tooltip.raw="Total Credit Unit">
                    Semester summary (TCU: {{ $semesterTotalCreditUnit }})
                </th>
                <th colspan="4" class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Total Credit Unit">
                    Cumulative summary (CTCU: {{ $cumulativeTotalCreditUnit }})
                </th>
            </tr>

            <tr class="bg-gray-50">
                <th class="border px-2 py-1"></th>
                <th class="border px-2 py-1"></th>
                <th class="border px-2 py-1"></th>

                @foreach ($courses as $course)
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Total Score">S</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade">G</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Point">P</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point">GP</th>
                @endforeach

                <th class="border px-2 py-1 text-center" x-tooltip.raw="Total Grade Point">TGP</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Remark">Remark</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Outstanding Courses">Outstanding</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Total Grade Point">CTGP</th>
                <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                <th class="border px-2 py-1 text-center whitespace-nowrap" x-tooltip.raw="Cumulative Remark">C. Remark
                </th>
                <th class="border px-2 py-1 text-center whitespace-nowrap"
                    x-tooltip.raw="Cumulative Outstanding Courses">C. Outstanding</th>
            </tr>
        </thead>

        <tbody>
            @forelse ($filteredRecords as $index => $record)
            @php
            $semesterTotalGradePoint = OverviewResource::getSemesterTotalGradePoint($livewire, $record, $courses);
            $gpa = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit, 2)
            : 0;
            $remark = OverviewResource::getRemarkFromGradePointAverage($gpa);

            $semesterOutstandingCourses = OverviewResource::getSemesterOutstandingCourses($livewire, $record, $courses);
            $outstandingCourses = $semesterOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';

            $cumulativeTotalGradePoint = OverviewResource::getCumulativeTotalGradePoint($livewire, $record);
            $cgpa = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
            $cumulativeTotalCreditUnit, 2) : 0;
            $cumulativeRemark = OverviewResource::getRemarkFromGradePointAverage($cgpa);

            $cumulativeOutstandingCourses = OverviewResource::getCumulativeOutstandingCourses($record, $livewire);
            $cumulativeOutstanding = $cumulativeOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';
            @endphp

            <tr>
                <td class="border px-2 py-1">{{ $index + 1 }}</td>
                <td class="border px-2 py-1 whitespace-nowrap">{{ $record->name }}</td>
                <td class="border px-2 py-1">{{ $record->matric_number }}</td>

                @foreach ($courses as $course)
                @php
                $courseData = OverviewResource::getCourseData($livewire, $record, $course);
                @endphp
                <td
                    class="border px-2 py-1 {{ ($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : '' }}">
                    {{ $courseData['total_score'] ?? '-' }}
                </td>
                <td
                    class="border px-2 py-1 {{ ($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : '' }}">
                    {{ $courseData['grade'] ?? '-' }}
                </td>
                <td class="border px-2 py-1">{{ $courseData['point'] ?? '-' }}</td>
                <td class="border px-2 py-1">{{ $courseData['grade_point'] ?? '-' }}</td>
                @endforeach

                <td class="border px-2 py-1">{{ $semesterTotalGradePoint }}</td>
                <td class="border px-2 py-1">{{ $gpa }}</td>
                <td class="border px-2 py-1 whitespace-nowrap">{{ $remark?->remark ?? '-' }}</td>
                <td class="border px-2 py-1">
                    <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">{{ $outstandingCourses }}</div>
                </td>

                <td class="border px-2 py-1">{{ $cumulativeTotalGradePoint }}</td>
                <td class="border px-2 py-1">{{ $cgpa }}</td>
                <td class="border px-2 py-1 whitespace-nowrap">{{ $cumulativeRemark?->remark ?? '-' }}</td>
                <td class="border px-2 py-1">
                    <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">{{ $cumulativeOutstanding }}</div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="{{ 11 + ($courses->count() * 4) }}" class="border px-2 py-2 text-center">
                    No students found.
                </td>
            </tr>
            @endforelse
        </tbody>
    </table>
</div>