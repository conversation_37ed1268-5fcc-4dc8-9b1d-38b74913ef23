<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">
    <title>{{ $fileName }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
        @media print {
            @page {
                size: A4 landscape;
                margin: 5mm;
            }

            .college-name-print {
                font-size: 20pt !important;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 8px;
            }

            h1 {
                font-size: 16px !important;
            }

            h2 {
                font-size: 14px !important;
            }

            h3 {
                font-size: 12px !important;
            }

            th,
            td {
                font-size: 8px !important;
                padding: 2px !important;
            }

            th,
            tfoot tr {
                background-color: #f9fafb !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            * {
                border-color: #6b7280 !important;
                border-radius: 2px !important;
            }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-full mx-auto p-2 text-sm space-y-4">
        {{-- HEADER --}}
        <x-document-simple-header :collegeLogo="asset('images/racoed-favicon.png')"
            :collegeName="$collegeSettings->name" heading="Examination & Records" subheading="Result Overview" />

        {{-- OVERVIEW DETAILS --}}
        <div class="border p-2">
            <h2 class="text-center font-bold mb-2">Overview Details</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-1">
                <div class="border p-1"><strong>Session:</strong> {{ $session ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Semester:</strong> {{ $semester ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Level:</strong> {{ $level ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Department:</strong> {{ $department ?? 'NIL' }}</div>
            </div>
        </div>

        {{-- OVERVIEW TABLE --}}
        <div class="border p-1">
            <h2 class="text-lg font-semibold mb-2 text-center">Overview Report</h2>

            <x-overview :livewire="$livewire" :filters="$filters" :filteredRecords="$filteredRecords"
                :courses="$courses" :grades="$grades" :failedScore="$failedScore"
                :semesterTotalCreditUnit="$semesterTotalCreditUnit"
                :cumulativeTotalCreditUnit="$cumulativeTotalCreditUnit" />

        </div>

        {{-- PRINT BUTTON --}}
        <div class="text-center print:hidden">
            <button onclick="window.print()"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Print Overview
            </button>
        </div>
    </div>

    @livewireScripts
    @filamentScripts
</body>

</html>