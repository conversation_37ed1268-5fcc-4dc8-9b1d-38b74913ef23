<?php
    $sidebar = $this->getSubNavigationPosition();
    $articleClass = \Guava\FilamentKnowledgeBase\Facades\KnowledgeBase::panel()->getArticleClass();
?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.querySelectorAll('.gu-kb-anchor')
            .forEach((element) => element.addEventListener('click', function (event) {
                window.Livewire.dispatch('documentation.anchor.copy', {
                    url: event.target.href
                });
            }))

        var filamentKnowledgeBaseTranslations = {
            urlCopied: "<?php echo app('translator')->get('filament-knowledge-base::translations.url-copied'); ?>",
            codeCopied: "<?php echo app('translator')->get('filament-knowledge-base::translations.code-copied'); ?>",
        };
    </script>
<?php $__env->stopPush(); ?>

<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => ['class' => \Illuminate\Support\Arr::toCssClasses([
        '[&_.fi-sidebar-group]:sticky [&_.fi-sidebar-group]:top-20',
        '[&_.fi-page-sub-navigation-sidebar]:pl-4 [&_.fi-page-sub-navigation-sidebar]:ml-4 [&_.fi-page-sub-navigation-sidebar]:border-l [&_.fi-page-sub-navigation-sidebar]:border-l-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-l-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::End,
        '[&_.fi-page-sub-navigation-sidebar]:pr-4 [&_.fi-page-sub-navigation-sidebar]:mr-4 [&_.fi-page-sub-navigation-sidebar]:border-r [&_.fi-page-sub-navigation-sidebar]:border-r-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-r-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::Start,
    ]),'fullHeight' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
        '[&_.fi-sidebar-group]:sticky [&_.fi-sidebar-group]:top-20',
        '[&_.fi-page-sub-navigation-sidebar]:pl-4 [&_.fi-page-sub-navigation-sidebar]:ml-4 [&_.fi-page-sub-navigation-sidebar]:border-l [&_.fi-page-sub-navigation-sidebar]:border-l-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-l-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::End,
        '[&_.fi-page-sub-navigation-sidebar]:pr-4 [&_.fi-page-sub-navigation-sidebar]:mr-4 [&_.fi-page-sub-navigation-sidebar]:border-r [&_.fi-page-sub-navigation-sidebar]:border-r-gray-600/10 [&_.fi-page-sub-navigation-sidebar]:dark:border-r-gray-600/30' => $sidebar === \Filament\Pages\SubNavigationPosition::Start,
    ])),'full-height' => true]); ?>
    <?php if (isset($component)) { $__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-knowledge-base::components.content','data' => ['class' => \Illuminate\Support\Arr::toCssClasses([
        'gu-kb-article-full',
        $articleClass => ! empty($articleClass),
    ])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-knowledge-base::content'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
        'gu-kb-article-full',
        $articleClass => ! empty($articleClass),
    ]))]); ?>
        <?php echo $this->record->getHtml(); ?>

     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5)): ?>
<?php $attributes = $__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5; ?>
<?php unset($__attributesOriginal942e6ccdfa9297cace30fedb5ffc20c5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5)): ?>
<?php $component = $__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5; ?>
<?php unset($__componentOriginal942e6ccdfa9297cace30fedb5ffc20c5); ?>
<?php endif; ?>


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Herd\racoed\vendor\guava\filament-knowledge-base\resources\views\documentation.blade.php ENDPATH**/ ?>