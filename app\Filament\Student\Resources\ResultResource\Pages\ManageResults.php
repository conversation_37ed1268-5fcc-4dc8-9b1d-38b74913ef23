<?php

namespace App\Filament\Student\Resources\ResultResource\Pages;

use App\Filament\Student\Resources\ResultResource;
use App\Models\Assessment;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\URL;

class ManageResults extends ManageRecords
{
    protected static string $resource = ResultResource::class;

    protected function getHeaderActions(): array
    {
        $user = Auth::user();

        if ($user->hasUnpaidPortalFee()) {
            return [
                Action::make('unpaid')
                    ->label('Cannot export: unpaid portal fees')
                    ->color('danger')
                    ->icon('heroicon-m-exclamation-triangle')
                    ->disabled(),
            ];
        }

        return [
            ActionGroup::make([
                Action::make('print')
                    ->disabled(fn () => $this->hasNoRequiredFiltersNorFilteredRecords())
                    ->icon('heroicon-m-printer')
                    ->action(function ($livewire) {
                        try {
                            $resultData = $this->getResultData();

                            $resultCacheKey = 'resultData_'.uniqid();
                            Cache::put($resultCacheKey, $resultData, now()->addMinutes(5));

                            $url = URL::signedRoute('result.print', ['resultCacheKey' => $resultCacheKey]);

                            $livewire->js("(function() {
                                    const newWindow = window.open(
                                        '$url',
                                        'Result',
                                        'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                    );

                                    if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                        alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                    } else {
                                        newWindow.focus();
                                    }
                                })();");
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Print Error')
                                ->body('Unable to print result. Please try again later.')
                                ->danger()
                                ->send();
                        }
                    }),
                Action::make('download')
                    ->disabled(fn () => $this->hasNoRequiredFiltersNorFilteredRecords())
                    ->icon('heroicon-m-document-arrow-down')
                    ->action(function ($livewire) {
                        try {
                            $resultData = $this->getResultData();

                            $resultCacheKey = 'resultData_'.uniqid();
                            Cache::put($resultCacheKey, $resultData, now()->addMinutes(5));

                            $url = URL::signedRoute('result.download', ['resultCacheKey' => $resultCacheKey]);

                            $livewire->js("window.location.href = '$url';");
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Download Error')
                                ->body('Unable to download result. Please try again later.')
                                ->danger()
                                ->send();
                        }
                    }),
            ])
                ->button()
                ->label(fn () => $this->hasNoRequiredFiltersNorFilteredRecords() ? 'Cannot export: view result first' : 'Export result')
                ->icon('heroicon-m-document-text'),
        ];
    }

    private function getResultData(): array
    {
        $student = $this->getFilteredTableQuery()->first();

        $assessmentNames = Assessment::pluck('max_score', 'name')->all();
        $courseData = ResultResource::getSemesterCourseData($this, $student) ?? [];

        // Semester calculations
        $semesterTotalCreditUnit = ResultResource::getSemesterTotalCreditUnit($courseData);
        $semesterTotalGradePoint = ResultResource::getSemesterTotalGradePoint($courseData);
        $semesterGradePointAverage = $semesterTotalCreditUnit > 0
            ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit, 2)
            : null;
        $semesterGradeRemark = ResultResource::getRemarkFromGradePointAverage($semesterGradePointAverage);
        $semesterOutstandingCourses = ResultResource::getSemesterOutstandingCourses($courseData);

        // Cumulative calculations
        $cumulativeTotalCreditUnit = ResultResource::getCumulativeTotalCreditUnit($this, $student);
        $cumulativeTotalGradePoint = ResultResource::getCumulativeTotalGradePoint($this, $student);
        $cumulativeGradePointAverage = $cumulativeTotalCreditUnit > 0
            ? number_format($cumulativeTotalGradePoint / $cumulativeTotalCreditUnit, 2)
            : null;
        $cumulativeGradeRemark = ResultResource::getRemarkFromGradePointAverage($cumulativeGradePointAverage);
        $cumulativeOutstandingCourses = ResultResource::getCumulativeOutstandingCourses($this, $student);

        return [
            'assessmentNames' => $assessmentNames,
            'courseData' => $courseData,

            'student' => $student,
            'tableFilters' => $this->extractFilters(),

            'semesterTotalCreditUnit' => $semesterTotalCreditUnit,
            'semesterTotalGradePoint' => $semesterTotalGradePoint,
            'semesterGradePointAverage' => $semesterGradePointAverage,
            'semesterGradeRemark' => $semesterGradeRemark,
            'semesterOutstandingCourses' => $semesterOutstandingCourses,

            'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit,
            'cumulativeTotalGradePoint' => $cumulativeTotalGradePoint,
            'cumulativeGradePointAverage' => $cumulativeGradePointAverage,
            'cumulativeGradeRemark' => $cumulativeGradeRemark,
            'cumulativeOutstandingCourses' => $cumulativeOutstandingCourses,
        ];
    }

    private function hasNoRequiredFiltersNorFilteredRecords(): bool
    {

        if (! $this->hasRequiredFilters()) {
            return true;
        }

        return $this->getFilteredRecords()->isEmpty();
    }

    private function getFilteredRecords()
    {
        return $this->getFilteredTableQuery()->get();
    }

    private function hasRequiredFilters(): bool
    {
        $filters = $this->extractFilters();

        return isset(
            $filters['school_session_id'],
            $filters['semester_id'],
            $filters['level_id'],
            $filters['department_id'],
        );
    }

    private function extractFilters(): array
    {
        $filters = $this->tableFilters['result_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
        ];
    }
}
