<?php

namespace App\Filament\Staff\Resources\StudentResource\Pages;

use App\Enums\Role;
use App\Models\User;
use App\Models\Level;
use App\Models\Semester;
use App\Models\Programme;
use App\Models\SchoolSession;
use Filament\Actions;
use Filament\Actions\Action;
use App\Enums\AdmissionStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\File;
use Filament\Resources\Components\Tab;
use Filament\Notifications\Notification;
use App\Filament\Imports\StudentImporter;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Staff\Resources\StudentResource;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Filament\Support\Enums\MaxWidth;

class ListStudents extends ListRecords
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->visible(fn() => Auth::user()->role === Role::ICT)
                ->label('Import Students')
                ->importer(StudentImporter::class)
                ->maxRows(100)
                ->fileRules([
                    File::types(['csv', 'txt'])->max(1024),
                ]),
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $filters = self::extractFilters($this);
        $filteredSessionId = $filters['school_session_id'] ?? null;
        $filteredSemesterId = $filters['semester_id'] ?? null;

        $registeredQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereDoesntHave('registrations', fn($q) => $q->where('is_graduated', true))
            ->whereDoesntHave('registrations', fn($q) => $q->where('is_withdrawn', true));

        $pendingQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::PENDING));

        $deniedQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::DENIED));

        $graduatedQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereHas('registrations', fn($q) => $q->where('is_graduated', true));

        $withdrawnQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereHas('registrations', fn($q) => $q->where('is_withdrawn', true));

        $getBadgeCount = function (callable $queryModifier) use ($filteredSessionId, $filteredSemesterId): int {
            $query = $this->getResource()::getEloquentQuery();

            $queryModifier($query);

            if ($filteredSessionId && $filteredSemesterId) {
                $query->whereHas('registrations', fn($q) => $q->where('school_session_id', $filteredSessionId)->where('semester_id', $filteredSemesterId));
            }

            return $query->count();
        };

        return [
            'registered' => Tab::make('registered')
                ->label('Registered')
                ->modifyQueryUsing($registeredQuery)
                ->badge($getBadgeCount($registeredQuery)),

            'pending' => Tab::make('pending')
                ->label('Pending')
                ->modifyQueryUsing($pendingQuery)
                ->badge($getBadgeCount($pendingQuery)),

            'denied' => Tab::make('denied')
                ->label('Denied')
                ->modifyQueryUsing($deniedQuery)
                ->badge($getBadgeCount($deniedQuery)),

            'graduated' => Tab::make('graduated')
                ->label('Graduated')
                ->modifyQueryUsing($graduatedQuery)
                ->badge($getBadgeCount($graduatedQuery)),

            'withdrawn' => Tab::make('withdrawn')
                ->label('Withdrawn')
                ->modifyQueryUsing($withdrawnQuery)
                ->badge($getBadgeCount($withdrawnQuery)),
        ];
    }

    private static function extractFilters($livewire): array
    {
        $filters = $livewire->tableFilters['session_semester_level_programme_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'programme_id' => $filters['programme_id'] ?? null,
        ];
    }

    public function printRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.print', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'Registration',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Notification::make()
                ->title('Print Error')
                ->body('Unable to print registration. Please try again later.' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function downloadRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.download', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Notification::make()
                ->title('Download Error')
                ->body('Unable to download registration. Please try again later.' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function confirmRegistrationsAction(): Action
    {
        return Action::make('confirmRegistrations')
            ->requiresConfirmation()
            ->modalHeading('Confirm bulk registration?')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->modalDescription(function (array $arguments) {
                $tableRecords = $arguments['tableRecords'];
                $registrationData = $arguments['registrationData'];

                $numberOfStudents = count($tableRecords);

                $newSession = SchoolSession::find($registrationData['school_session_id'])?->name ?? '';
                $newSemester = Semester::find($registrationData['semester_id'])?->name ?? '';
                $newLevel = Level::find($registrationData['level_id'])?->name ?? '';
                $newProgramme = Programme::find($registrationData['programme_id'])?->name ?? '';

                return new HtmlString("
                    Are you sure you want to register <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b> to:
                    <br><b>{$newSession}</b> session,
                    <b>{$newSemester}</b>,
                    <b>{$newLevel}</b> level,
                    <b>{$newProgramme}</b> programme?
                    <br><br><i>Note: This will create new registrations or update existing ones for the same session and semester.</i>
                    <br><i>This action cannot be undone.</i>
                ");
            })
            ->action(function ($arguments) {
                $tableRecords = $arguments['tableRecords'];
                $registrationData = $arguments['registrationData'];

                $studentCounts = DB::transaction(function () use ($tableRecords, $registrationData) {
                    $updated = 0;
                    $created = 0;
                    $skipped = 0;

                    foreach ($tableRecords as $studentData) {
                        try {
                            $record = User::find($studentData['id']);
                            if (!$record) continue;

                            $alreadyRegistered = $record->registrations()
                                ->where('school_session_id', $registrationData['school_session_id'])
                                ->where('semester_id', $registrationData['semester_id'])
                                ->where('level_id', $registrationData['level_id'])
                                ->where('programme_id', $registrationData['programme_id'])
                                ->exists();

                            if ($alreadyRegistered) {
                                $skipped++;
                                continue;
                            }

                            $sameSessionAndSemester = $record->registrations()
                                ->where('school_session_id', $registrationData['school_session_id'])
                                ->where('semester_id', $registrationData['semester_id'])
                                ->first();

                            if (($registrationData['is_active'] ?? false) === true) {
                                $record->registrations()
                                    ->when($sameSessionAndSemester, fn($q) => $q->where('id', '!=', $sameSessionAndSemester->id))
                                    ->update(['is_active' => false]);
                            }

                            if ($sameSessionAndSemester) {
                                $sameSessionAndSemester->update([
                                    'semester_id' => $registrationData['semester_id'],
                                    'level_id' => $registrationData['level_id'],
                                    'programme_id' => $registrationData['programme_id'],
                                    'is_active' => $registrationData['is_active'] ?? false,
                                ]);
                                $updated++;
                            } else {
                                $record->registrations()->create($registrationData);
                                $created++;
                            }
                        } catch (\Exception $e) {
                            $skipped++;
                        }
                    }

                    return ['updated' => $updated, 'created' => $created, 'skipped' => $skipped];
                });

                $newSession = SchoolSession::find($registrationData['school_session_id'])?->name ?? '';
                $newSemester = Semester::find($registrationData['semester_id'])?->name ?? '';
                $newLevel = Level::find($registrationData['level_id'])?->name ?? '';
                $newProgramme = Programme::find($registrationData['programme_id'])?->name ?? '';

                if ($studentCounts['created'] > 0) {
                    Notification::make()
                        ->success()
                        ->title('Students Registered')
                        ->body("<b>{$studentCounts['created']} " . Str::plural('student', $studentCounts['created']) . "</b> registered to <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                        ->send();
                }

                if ($studentCounts['updated'] > 0) {
                    Notification::make()
                        ->success()
                        ->title('Registrations Updated')
                        ->body("<b>{$studentCounts['updated']} " . Str::plural('student', $studentCounts['updated']) . "</b> registration updated for <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                        ->send();
                }

                if ($studentCounts['skipped'] > 0 && $studentCounts['created'] === 0 && $studentCounts['updated'] === 0) {
                    Notification::make()
                        ->warning()
                        ->title('No Students Processed')
                        ->body("<b>{$studentCounts['skipped']} " . Str::plural('student', $studentCounts['skipped']) . "</b> skipped due to existing registrations or errors.")
                        ->send();
                }
            });
    }

    public function confirmGraduationsAction(): Action
    {
        return Action::make('confirmGraduations')
            ->requiresConfirmation()
            ->modalHeading('Confirm bulk graduation?')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->modalDescription(function (array $arguments) {
                $tableRecords = $arguments['tableRecords'];
                $sessionName = $arguments['sessionName'];

                $numberOfStudents = count($tableRecords);

                return new HtmlString("
                    Are you sure you'd like to graduate <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b> from <b>{$sessionName}</b> session?
                    <br><br>The students will not have active registration but their records will remain in the portal and they will have access to them.
                    <br><br><i>This action cannot be undone.</i>
                ");
            })
            ->action(function ($arguments) {
                $tableRecords = $arguments['tableRecords'];
                $filteredSessionId = $arguments['filteredSessionId'];
                $filteredSemesterId = $arguments['filteredSemesterId'];
                $sessionName = $arguments['sessionName'];

                $successCount = 0;
                $skippedStudents = [];

                foreach ($tableRecords as $studentData) {
                    $record = User::find($studentData['id']);
                    if ($record) {                  

                        // Check if student has outstanding courses across all semesters
                        $outstandingCourses = StudentResource::getCumulativeOutstandingCourses($record);

                        if ($outstandingCourses->isNotEmpty()) {
                            $skippedStudents[] = [
                                'name' => $record->name,
                                'courses' => $outstandingCourses->map(fn($course) => $course->code)->join(', ')
                            ];
                            continue; // Skip this student
                        }

                        if ($filteredSessionId && $filteredSemesterId) {
                            $record->registrations()
                                ->where('school_session_id', $filteredSessionId)
                                ->where('semester_id', $filteredSemesterId)
                                ->update([
                                    'is_graduated' => true,
                                ]);
                        }

                        $record->registrations()->update(['is_active' => false]);
                        $successCount++;
                    }
                }

                // Show success notification
                if ($successCount > 0) {
                    Notification::make()
                        ->success()
                        ->title('Students Graduated')
                        ->body("<b>{$successCount} " . Str::plural('student', $successCount) . "</b> graduated from <b>{$sessionName}</b> session successfully.")
                        ->send();
                }

                // Show warning notification for skipped students
                if (!empty($skippedStudents)) {
                    $skippedList = collect($skippedStudents)->map(fn($student) =>
                        "<b>{$student['name']}</b> (Outstanding: {$student['courses']})"
                    )->join('<br>');

                    Notification::make()
                        ->warning()
                        ->title('Students Skipped')
                        ->body(new HtmlString("The following students were skipped due to outstanding courses:<br><br>{$skippedList}"))
                        ->persistent()
                        ->send();
                }
            });
    }

    public function confirmWithdrawalsAction(): Action
    {
        return Action::make('confirmWithdrawals')
            ->requiresConfirmation()
            ->modalHeading('Confirm bulk withdrawal?')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->modalDescription(function (array $arguments) {
                $tableRecords = $arguments['tableRecords'];
                $sessionName = $arguments['sessionName'];

                $numberOfStudents = count($tableRecords);

                return new HtmlString("
                    Are you sure you'd like to withdraw <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b> from <b>{$sessionName}</b> session?
                    <br><br>The students will not have active registration but their records will remain in the portal and they will have access to them.
                    <br><br><i>This action cannot be undone.</i>
                ");
            })
            ->action(function ($arguments) {
                $tableRecords = $arguments['tableRecords'];
                $filteredSessionId = $arguments['filteredSessionId'];
                $sessionName = $arguments['sessionName'];

                $successCount = 0;
                foreach ($tableRecords as $studentData) {
                    $record = User::find($studentData['id']);
                    if ($record) {
                        $record->registrations()
                            ->where('school_session_id', $filteredSessionId)
                            ->update([
                                'is_withdrawn' => true,
                                'is_active' => false,
                            ]);
                        $successCount++;
                    }
                }

                Notification::make()
                    ->success()
                    ->title('Students Withdrawn')
                    ->body("<b>{$successCount} " . Str::plural('student', $successCount) . "</b> withdrawn from <b>{$sessionName}</b> session successfully.")
                    ->send();
            });
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'registered';
    }
}
